<mat-sidenav-container>
  <mat-sidenav #sidenav [mode]="isMobile ? 'over' : 'side'" [opened]="!isMobile" [fixedInViewport]="true">
    <!-- 移动端菜单按钮 -->
    <div *ngIf="isMobile" class="mobile-menu-header">
      <button mat-icon-button (click)="sidenav.close()">
        <mat-icon>close</mat-icon>
      </button>
    </div>
    <mat-nav-list>
      <!-- 加载状态 -->
      <div *ngIf="isLoading" class="loading-container">
        <p>Loading categories...</p>
      </div>

      <!-- 错误状态 -->
      <div *ngIf="error" class="error-container">
        <p>{{ error }}</p>
        <button mat-button (click)="loadCategories()">Retry</button>
      </div>

      <!-- 分类列表 - 方块网格布局 -->
      <div class="category-grid">
        <div *ngFor="let category of categories; let i = index; trackBy: trackByCategories"
             class="category-block"
             [class.active-category]="i === currentCategoryIndex"
             (click)="scrollToCategory(category.route.substring(1)); onCategoryChange(i); isMobile && sidenav.close()">
          <div class="category-icon">
            <img *ngIf="category.imagePath; else fallbackIcon"
                 [src]="category.imagePath"
                 [alt]="category.name"
                 (error)="onCategoryImageError($event, category)"
                 loading="lazy">
            <ng-template #fallbackIcon>
              <mat-icon>{{ getCategoryIcon(category.name) }}</mat-icon>
            </ng-template>
          </div>
          <div class="category-text">{{ category.name }}</div>
        </div>
      </div>
    </mat-nav-list>
  </mat-sidenav>

  <mat-sidenav-content>
    <!-- 移动端菜单按钮 -->
    <div *ngIf="isMobile" class="mobile-menu-toggle">
      <button mat-icon-button (click)="sidenav.toggle()">
        <mat-icon>menu</mat-icon>
      </button>
    </div>
    <app-category
      [categories]="categories"
      [isLoading]="isLoading"
      (categoryChange)="onCategoryChange($event)">
    </app-category>
  </mat-sidenav-content>
</mat-sidenav-container>

<!-- 底部购物车栏 -->
<app-bottom-cart-bar></app-bottom-cart-bar>

<!-- 超时警告组件 -->
<app-timeout-warning></app-timeout-warning>
