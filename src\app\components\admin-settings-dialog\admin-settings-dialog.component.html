<div class="admin-settings-dialog">
  <div class="dialog-header">
    <h2 mat-dialog-title>
      <mat-icon class="header-icon">settings</mat-icon>
      {{ data.title || 'Administrator Settings' }}
    </h2>
  </div>

  <div mat-dialog-content class="dialog-content">
    <div class="settings-section">
      <div class="section-header">
        <mat-icon class="section-icon">store</mat-icon>
        <h3>Store Configuration</h3>
      </div>

      <div class="section-description">
        <p>Configure the unique identifier for the current store, used for order management and data synchronization.</p>
      </div>

      <mat-form-field appearance="outline" class="store-id-field">
        <mat-label>Store ID</mat-label>
        <input matInput
               [formControl]="storeIdControl"
               placeholder="Enter Store ID"
               autocomplete="off"
               maxlength="50">
        <mat-icon matSuffix>business</mat-icon>
        <mat-hint align="start">Unique identifier for the store</mat-hint>
        <mat-hint align="end">{{ (storeIdControl.value || '').length }}/50</mat-hint>
        <mat-error *ngIf="storeIdControl.invalid && storeIdControl.touched">
          {{ getStoreIdErrorMessage() }}
        </mat-error>
      </mat-form-field>

      <div class="field-actions">
        <button mat-button
                (click)="onClear()"
                class="clear-button"
                [disabled]="isLoading">
          <mat-icon>clear</mat-icon>
          Clear
        </button>
        <button mat-button
                (click)="onReset()"
                class="reset-button"
                [disabled]="isLoading || !hasUnsavedChanges()">
          <mat-icon>refresh</mat-icon>
          Reset
        </button>
      </div>
    </div>

    <!-- Timeout Configuration Section -->
    <div class="settings-section">
      <div class="section-header">
        <mat-icon class="section-icon">schedule</mat-icon>
        <h3>Timeout Configuration</h3>
      </div>

      <div class="section-description">
        <p>Configure the timeout duration for automatic return to home page when user is inactive.</p>
      </div>

      <mat-form-field appearance="outline" class="timeout-field">
        <mat-label>Timeout (minutes)</mat-label>
        <input matInput
               type="number"
               [formControl]="timeoutControl"
               placeholder="5"
               min="1"
               max="60"
               autocomplete="off">
        <mat-icon matSuffix>timer</mat-icon>
        <mat-hint align="start">Auto-return timeout in minutes (1-60)</mat-hint>
        <mat-hint align="end">{{ timeoutControl.value || 0 }} minutes</mat-hint>
        <mat-error *ngIf="timeoutControl.invalid && timeoutControl.touched">
          {{ getTimeoutErrorMessage() }}
        </mat-error>
      </mat-form-field>

      <div class="field-actions">
        <button mat-button
                (click)="onResetTimeout()"
                class="reset-timeout-button"
                [disabled]="isLoading">
          <mat-icon>restore</mat-icon>
          Default (5 min)
        </button>
      </div>
    </div>

    <div class="info-section" *ngIf="originalStoreId || originalTimeoutMinutes">
      <div class="info-header">
        <mat-icon class="info-icon">info</mat-icon>
        <span>Current Configuration</span>
      </div>
      <div class="current-config">
        <div class="config-item" *ngIf="originalStoreId">
          <span class="config-label">Current Store ID:</span>
          <span class="config-value">{{ originalStoreId }}</span>
        </div>
        <div class="config-item">
          <span class="config-label">Current Timeout:</span>
          <span class="config-value">{{ originalTimeoutMinutes }} minutes</span>
        </div>
      </div>
    </div>

    <div class="warning-section" *ngIf="hasUnsavedChanges()">
      <mat-icon class="warning-icon">warning</mat-icon>
      <span>You have unsaved changes</span>
    </div>
  </div>

  <div mat-dialog-actions class="dialog-actions">
    <button mat-button
            (click)="onCancel()"
            class="cancel-button"
            [disabled]="isLoading">
      Cancel
    </button>
    <button mat-raised-button
            color="primary"
            (click)="onSave()"
            [disabled]="storeIdControl.invalid || timeoutControl.invalid || isLoading || !hasUnsavedChanges()"
            class="save-button">
      <mat-icon *ngIf="isLoading" class="loading-icon">hourglass_empty</mat-icon>
      <mat-icon *ngIf="!isLoading">save</mat-icon>
      {{ isLoading ? 'Saving...' : 'Save' }}
    </button>
  </div>
</div>
