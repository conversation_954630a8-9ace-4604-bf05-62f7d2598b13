import { bootstrapApplication } from '@angular/platform-browser';
import { AppComponent } from './app/app.component';
import { provideRouter } from '@angular/router';
import { routes } from './app/app.routes';
import { provideAnimations } from '@angular/platform-browser/animations';
import { provideHttpClient } from '@angular/common/http';
import { DeliOrderingFoodCategoryClient, DeliOrderingFoodItemClient, DeliOrderingFoodItemImageGalleryClient, DeliOrderingFoodCartClient, DeliOrderingFoodOrderClient, StoreConnectionClient, StoresClient, API_BASE_URL } from './app/service/backoffice';
import { environment } from './environments/environment';

bootstrapApplication(AppComponent, {
  providers: [
    provideRouter(routes),
    provideAnimations(),
    provideHttpClient(),
    // API客户端配置
    DeliOrderingFoodCategoryClient,
    DeliOrderingFoodItemClient,
    DeliOrderingFoodItemImageGalleryClient,
    DeliOrderingFoodCartClient,
    DeliOrderingFoodOrderClient,
    StoreConnectionClient,
    StoresClient,
    { provide: API_BASE_URL, useValue: environment.apiRoot }
  ]
}).catch(err => console.error(err));