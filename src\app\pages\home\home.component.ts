import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatDialog } from '@angular/material/dialog';
import { PasswordDialogComponent } from '../../components/password-dialog/password-dialog.component';
import { AdminSettingsDialogComponent } from '../../components/admin-settings-dialog/admin-settings-dialog.component';
import { StorageService } from '../../service/storage.service';
import { StoresClient } from '../../service/backoffice';

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [CommonModule, MatIconModule],
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.css']
})
export class HomeComponent implements OnInit {
  // 页面标题和背景图片
  pageTitle = 'Welcome to Our Restaurant';
  pageSubtitle = 'Experience delicious American food';
  backgroundImageUrl = '';

  constructor(
    private router: Router,
    private dialog: MatDialog,
    private storageService: StorageService,
    private storesClient: StoresClient
  ) {}

  ngOnInit(): void {
    this.loadPageSettings();
  }

  /**
   * 加载页面设置
   */
  private loadPageSettings(): void {
    const storeIdStr = this.storageService.getStoreId();
    if (!storeIdStr) {
      console.warn('未找到storeId，使用默认设置');
      return;
    }

    const storeId = parseInt(storeIdStr, 10);
    if (isNaN(storeId)) {
      console.error('storeId格式错误:', storeIdStr);
      return;
    }

    console.log('获取商店设置，storeId:', storeId);

    this.storesClient.getSelfOrderingSetting(storeId).subscribe({
      next: (response) => {
        console.log('API返回:', response.result);
        if (response.result && response.result.selfOrderingSettingItems) {
          // 遍历设置项，找到标题和背景图片
          response.result.selfOrderingSettingItems.forEach(item => {
            if (item.fieldName && item.value) {
              switch (item.fieldName.toLowerCase()) {
                case 'title':
                case 'restaurantname':
                case 'storename':
                  this.pageTitle = item.value;
                  break;
                case 'subtitle':
                case 'description':
                case 'welcomemessage':
                  this.pageSubtitle = item.value;
                  break;
                case 'backgroundimage':
                case 'background_image':
                case 'bgimage':
                  this.backgroundImageUrl = item.value;
                  break;
              }
            }
          });
          console.log('设置已更新:', {
            title: this.pageTitle,
            subtitle: this.pageSubtitle,
            backgroundImage: this.backgroundImageUrl
          });
        }
      },
      error: (error) => {
        console.error('获取页面设置失败:', error);
        // 使用默认设置，不影响页面正常显示
      }
    });
  }

  // 导航到Asian食物页面
  startOrdering(): void {
    this.router.navigate(['/asian']);
  }

  /**
   * 打开管理员设置
   */
  openAdminSettings(): void {
    // 首先打开密码验证对话框
    const passwordDialogRef = this.dialog.open(PasswordDialogComponent, {
      width: '450px',
      disableClose: true,
      data: {
        title: 'Administrator Verification',
        message: 'Please enter administrator password to access settings'
      }
    });

    passwordDialogRef.afterClosed().subscribe(result => {
      if (result === true) {
        // 密码验证通过，打开设置对话框
        this.openSettingsDialog();
      }
      // 如果result为false或undefined，表示取消或密码错误，不做任何操作
    });
  }

  /**
   * 打开设置对话框
   */
  private openSettingsDialog(): void {
    const settingsDialogRef = this.dialog.open(AdminSettingsDialogComponent, {
      width: '550px',
      disableClose: true,
      data: {
        title: '管理员设置'
      }
    });

    settingsDialogRef.afterClosed().subscribe(result => {
      if (result && result.saved) {
        console.log('设置已保存:', result.storeId);
        // 这里可以添加额外的逻辑，比如刷新某些数据
      }
    });
  }
}
