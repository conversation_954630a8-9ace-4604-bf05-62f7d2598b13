import { Component, <PERSON><PERSON>ni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { UserActivityService } from '../../service/user-activity.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-timeout-warning',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule
  ],
  template: `
    <div class="timeout-warning-backdrop" *ngIf="isVisible">
      <div class="timeout-warning-container">
        <div class="warning-card">
          <div class="warning-content">
            <div class="warning-icon">
              <mat-icon>access_time</mat-icon>
            </div>
            
            <h2 class="warning-title">The conversation is about to exceed the time limit.</h2>
            
            <div class="countdown-section">
              <div class="countdown-number">{{ remainingSeconds }}</div>
              <div class="countdown-label">Will automatically return to the home page after a second.</div>
            </div>
            
            <div class="progress-bar">
              <div 
                class="progress-fill" 
                [style.width.%]="(remainingSeconds / 30) * 100">
              </div>
            </div>
            
            <div class="warning-actions">
              <button 
                mat-button 
                class="continue-button"
                (click)="extendSession()">
               Continue to use
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .timeout-warning-backdrop {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background: rgba(0, 0, 0, 0.6);
      z-index: 10000;
      display: flex;
      align-items: center;
      justify-content: center;
      animation: fadeIn 0.3s ease-out;
      backdrop-filter: blur(2px);
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
      }
      to {
        opacity: 1;
      }
    }

    .timeout-warning-container {
      animation: scaleIn 0.3s ease-out;
    }

    @keyframes scaleIn {
      from {
        transform: scale(0.9);
        opacity: 0;
      }
      to {
        transform: scale(1);
        opacity: 1;
      }
    }

    .warning-card {
      background: white;
      border-radius: 16px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
      min-width: 320px;
      max-width: 400px;
      margin: 20px;
    }

    .warning-content {
      padding: 32px 24px 24px;
      text-align: center;
    }

    .warning-icon {
      margin-bottom: 16px;
    }

    .warning-icon mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      color: #666;
    }

    .warning-title {
      margin: 0 0 24px 0;
      font-size: 20px;
      font-weight: 500;
      color: #333;
      letter-spacing: 0.5px;
    }

    .countdown-section {
      margin-bottom: 24px;
    }

    .countdown-number {
      font-size: 64px;
      font-weight: 300;
      color: #1976d2;
      line-height: 1;
      margin-bottom: 8px;
      font-family: 'Roboto Mono', monospace;
    }

    .countdown-label {
      font-size: 14px;
      color: #666;
      margin-bottom: 20px;
    }

    .progress-bar {
      width: 100%;
      height: 4px;
      background: #e3f2fd;
      border-radius: 2px;
      overflow: hidden;
      margin-bottom: 24px;
    }

    .progress-fill {
      height: 100%;
      background: #1976d2;
      border-radius: 2px;
      transition: width 1s ease-out;
    }

    .warning-actions {
      display: flex;
      justify-content: center;
    }

    .continue-button {
      padding: 12px 32px;
      font-size: 16px;
      font-weight: 500;
      color: #1976d2;
      background: transparent;
      border: 2px solid #1976d2;
      border-radius: 8px;
      transition: all 0.2s ease;
      min-width: 140px;
    }

    .continue-button:hover {
      background: #1976d2;
      color: white;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
    }

    .continue-button:active {
      transform: translateY(0);
    }

    @media (max-width: 480px) {
      .warning-card {
        min-width: auto;
        margin: 16px;
        max-width: calc(100vw - 32px);
      }
      
      .warning-content {
        padding: 24px 20px 20px;
      }
      
      .countdown-number {
        font-size: 48px;
      }
      
      .warning-title {
        font-size: 18px;
      }
    }
  `]
})
export class TimeoutWarningComponent implements OnInit, OnDestroy {
  isVisible = false;
  remainingSeconds = 30;
  private subscription: Subscription = new Subscription();

  constructor(private userActivityService: UserActivityService) {}

  ngOnInit(): void {
    // 订阅警告事件
    this.subscription = this.userActivityService.warning$.subscribe(warning => {
      this.isVisible = warning.show;
      if (warning.remainingSeconds !== undefined) {
        this.remainingSeconds = warning.remainingSeconds;
      }
    });
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  /**
   * 延长会话（通过触发用户活动来重置计时器）
   */
  extendSession(): void {
    // 模拟用户活动，重置计时器
    document.dispatchEvent(new Event('click'));
  }
}
