import { Component, OnInit, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormControl, Validators } from '@angular/forms';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { StorageService } from '../../service/storage.service';

export interface AdminSettingsDialogData {
  title?: string;
}

@Component({
  selector: 'app-admin-settings-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatSnackBarModule
  ],
  templateUrl: './admin-settings-dialog.component.html',
  styleUrls: ['./admin-settings-dialog.component.scss']
})
export class AdminSettingsDialogComponent implements OnInit {
  storeIdControl = new FormControl('', [
    Validators.required,
    Validators.minLength(1),
    Validators.maxLength(50)
  ]);
  
  timeoutControl = new FormControl(5, [
    Validators.required,
    Validators.min(1),
    Validators.max(60)
  ]);
  
  originalStoreId = '';
  originalTimeoutMinutes = 5;
  isLoading = false;

  constructor(
    public dialogRef: MatDialogRef<AdminSettingsDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: AdminSettingsDialogData,
    private storageService: StorageService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    // 加载当前的storeId和超时时间
    this.loadCurrentStoreId();
    this.loadCurrentTimeout();
  }

  /**
   * 加载当前存储的storeId
   */
  private loadCurrentStoreId(): void {
    const currentStoreId = this.storageService.getStoreId();
    this.originalStoreId = currentStoreId;
    this.storeIdControl.setValue(currentStoreId);
  }

  /**
   * 加载当前存储的超时时间
   */
  private loadCurrentTimeout(): void {
    const currentTimeout = this.storageService.getTimeoutMinutes();
    this.originalTimeoutMinutes = currentTimeout;
    this.timeoutControl.setValue(currentTimeout);
  }

  /**
   * 检查是否有未保存的更改
   */
  hasUnsavedChanges(): boolean {
    const currentStoreId = this.storeIdControl.value || '';
    const currentTimeout = this.timeoutControl.value || 5;
    
    return currentStoreId.trim() !== this.originalStoreId.trim() ||
           currentTimeout !== this.originalTimeoutMinutes;
  }

  /**
   * 保存设置
   */
  async onSave(): Promise<void> {
    if (this.storeIdControl.invalid || this.timeoutControl.invalid) {
      this.markFormGroupTouched();
      return;
    }

    this.isLoading = true;
    
    try {
      const newStoreId = this.storeIdControl.value || '';
      const newTimeout = this.timeoutControl.value || 5;
      
      const storeIdSuccess = this.storageService.setStoreId(newStoreId);
      const timeoutSuccess = this.storageService.setTimeoutMinutes(newTimeout);
      
      if (storeIdSuccess && timeoutSuccess) {
        this.originalStoreId = newStoreId.trim();
        this.originalTimeoutMinutes = newTimeout;
        this.showSuccessMessage('Settings saved successfully');

        // 延迟关闭对话框，让用户看到成功消息
        setTimeout(() => {
          this.dialogRef.close({
            saved: true,
            storeId: newStoreId.trim(),
            timeoutMinutes: newTimeout
          });
        }, 1000);
      } else {
        this.showErrorMessage('Save failed, please try again');
      }
    } catch (error) {
      console.error('保存设置时出错:', error);
      this.showErrorMessage('Save failed, please try again');
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 取消操作
   */
  onCancel(): void {
    if (this.hasUnsavedChanges()) {
      // 如果有未保存的更改，可以添加确认对话框
      // 这里简化处理，直接关闭
    }
    
    this.dialogRef.close({
      saved: false,
      storeId: this.originalStoreId,
      timeoutMinutes: this.originalTimeoutMinutes
    });
  }

  /**
   * 重置为原始值
   */
  onReset(): void {
    this.storeIdControl.setValue(this.originalStoreId);
    this.timeoutControl.setValue(this.originalTimeoutMinutes);
  }

  /**
   * 清空storeId
   */
  onClear(): void {
    this.storeIdControl.setValue('');
  }

  /**
   * 重置超时时间为默认值
   */
  onResetTimeout(): void {
    this.timeoutControl.setValue(5);
  }

  /**
   * 标记表单为已触摸，显示验证错误
   */
  private markFormGroupTouched(): void {
    this.storeIdControl.markAsTouched();
    this.timeoutControl.markAsTouched();
  }

  /**
   * 显示成功消息
   */
  private showSuccessMessage(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      panelClass: ['success-snackbar'],
      horizontalPosition: 'center',
      verticalPosition: 'top'
    });
  }

  /**
   * 显示错误消息
   */
  private showErrorMessage(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar'],
      horizontalPosition: 'center',
      verticalPosition: 'top'
    });
  }

  /**
   * 获取storeId输入框的错误消息
   */
  getStoreIdErrorMessage(): string {
    if (this.storeIdControl.hasError('required')) {
      return 'Store ID cannot be empty';
    }
    if (this.storeIdControl.hasError('minlength')) {
      return 'Store ID must be at least 1 character';
    }
    if (this.storeIdControl.hasError('maxlength')) {
      return 'Store ID cannot exceed 50 characters';
    }
    return '';
  }

  /**
   * 获取超时时间输入框的错误消息
   */
  getTimeoutErrorMessage(): string {
    if (this.timeoutControl.hasError('required')) {
      return 'Timeout cannot be empty';
    }
    if (this.timeoutControl.hasError('min')) {
      return 'Timeout must be at least 1 minute';
    }
    if (this.timeoutControl.hasError('max')) {
      return 'Timeout cannot exceed 60 minutes';
    }
    return '';
  }
}
