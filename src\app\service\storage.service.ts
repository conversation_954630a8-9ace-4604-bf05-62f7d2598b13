import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class StorageService {
  private readonly STORE_ID_KEY = 'restaurant_store_id';
  private readonly TIMEOUT_KEY = 'restaurant_timeout_minutes';
  private readonly DEFAULT_TIMEOUT_MINUTES = 5;

  constructor() {}

  /**
   * 获取存储的storeId
   * @returns storeId字符串，如果不存在则返回空字符串
   */
  getStoreId(): string {
    try {
      return localStorage.getItem(this.STORE_ID_KEY) || '';
    } catch (error) {
      console.error('获取storeId失败:', error);
      return '';
    }
  }

  /**
   * 保存storeId到本地存储
   * @param storeId 要保存的storeId
   * @returns 是否保存成功
   */
  setStoreId(storeId: string): boolean {
    try {
      if (storeId.trim()) {
        localStorage.setItem(this.STORE_ID_KEY, storeId.trim());
      } else {
        localStorage.removeItem(this.STORE_ID_KEY);
      }
      return true;
    } catch (error) {
      console.error('保存storeId失败:', error);
      return false;
    }
  }

  /**
   * 删除存储的storeId
   * @returns 是否删除成功
   */
  removeStoreId(): boolean {
    try {
      localStorage.removeItem(this.STORE_ID_KEY);
      return true;
    } catch (error) {
      console.error('删除storeId失败:', error);
      return false;
    }
  }

  /**
   * 检查是否已设置storeId
   * @returns 是否已设置storeId
   */
  hasStoreId(): boolean {
    return this.getStoreId().length > 0;
  }

  /**
   * 获取当前日期作为密码格式 (MMDDYYYY)
   * @returns 格式化的日期字符串
   */
  getCurrentDatePassword(): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    return `${month}${day}${year}`;
  }

  /**
   * 验证密码是否为当前日期
   * @param password 输入的密码
   * @returns 是否验证通过
   */
  validateDatePassword(password: string): boolean {
    const currentDatePassword = this.getCurrentDatePassword();
    return password.trim() === currentDatePassword;
  }

  /**
   * 获取存储的超时时间（分钟）
   * @returns 超时时间（分钟），如果不存在则返回默认值5分钟
   */
  getTimeoutMinutes(): number {
    try {
      const timeoutStr = localStorage.getItem(this.TIMEOUT_KEY);
      if (timeoutStr) {
        const timeout = parseInt(timeoutStr, 10);
        if (!isNaN(timeout) && timeout > 0 && timeout <= 60) {
          return timeout;
        }
      }
      return this.DEFAULT_TIMEOUT_MINUTES;
    } catch (error) {
      console.error('获取超时时间失败:', error);
      return this.DEFAULT_TIMEOUT_MINUTES;
    }
  }

  /**
   * 保存超时时间到本地存储
   * @param timeoutMinutes 要保存的超时时间（分钟），范围1-60
   * @returns 是否保存成功
   */
  setTimeoutMinutes(timeoutMinutes: number): boolean {
    try {
      if (timeoutMinutes > 0 && timeoutMinutes <= 60) {
        localStorage.setItem(this.TIMEOUT_KEY, timeoutMinutes.toString());
      } else {
        // 如果超出范围，使用默认值
        localStorage.setItem(this.TIMEOUT_KEY, this.DEFAULT_TIMEOUT_MINUTES.toString());
      }
      return true;
    } catch (error) {
      console.error('保存超时时间失败:', error);
      return false;
    }
  }

  /**
   * 删除存储的超时时间
   * @returns 是否删除成功
   */
  removeTimeoutMinutes(): boolean {
    try {
      localStorage.removeItem(this.TIMEOUT_KEY);
      return true;
    } catch (error) {
      console.error('删除超时时间失败:', error);
      return false;
    }
  }

  /**
   * 检查是否已设置自定义超时时间
   * @returns 是否已设置自定义超时时间
   */
  hasCustomTimeout(): boolean {
    return localStorage.getItem(this.TIMEOUT_KEY) !== null;
  }
}
